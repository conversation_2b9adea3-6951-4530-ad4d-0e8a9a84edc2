import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:common/models/game_match.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/game_config.dart';

import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';




import 'package:dauntless/use_cases/player_slot_management_use_case.dart';
import 'package:dauntless/use_cases/match_source_management_use_case.dart';
import 'package:dauntless/use_cases/match_data_synchronization_use_case.dart';
import 'package:dauntless/use_cases/match_joining_use_case.dart';
import 'package:dauntless/use_cases/websocket_management_use_case.dart';
import 'package:dauntless/use_cases/server_connection_use_case.dart';
import 'package:dauntless/use_cases/interfaces/server_connection_use_case_interface.dart';
import 'package:dauntless/use_cases/repository_management_use_case.dart';
import 'package:dauntless/use_cases/match_refresh_use_case.dart';
import 'package:dauntless/use_cases/player_slot_operations_use_case.dart';

import 'package:dauntless/use_cases/interfaces/player_slot_operations_use_case_interface.dart';
import 'package:dauntless/use_cases/match_lifecycle_use_case.dart';
import 'package:dauntless/use_cases/config_management_use_case.dart';
import 'package:dauntless/use_cases/interfaces/match_lifecycle_use_case_interface.dart' as lifecycle;
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart' as results;
import 'match_management_event.dart';
import 'match_management_state.dart';

/// Consolidated BLoC for match management
/// Combines functionality from MatchSelectionBloc, CreateMatchBloc, and MatchSelectionEnvironmentManager
/// Uses repository interfaces from Phase 1 for data access
class MatchManagementBloc extends Bloc<MatchManagementEvent, MatchManagementState> {
  // ========================================================================
  // DEPENDENCIES
  // ========================================================================

  final GameConfigUseCase _gameConfigUseCase;
  final RemoteLogger _logger;

  // New use cases for business logic
  final PlayerSlotManagementUseCase _playerSlotManagementUseCase;
  final MatchSourceManagementUseCase _matchSourceManagementUseCase;
  final MatchDataSynchronizationUseCase _matchDataSynchronizationUseCase;
  final MatchJoiningUseCase _matchJoiningUseCase;
  final WebSocketManagementUseCase _webSocketManagementUseCase;
  final ServerConnectionUseCase _serverConnectionUseCase;
  final RepositoryManagementUseCase _repositoryManagementUseCase;
  final MatchRefreshUseCase _matchRefreshUseCase;
  final PlayerSlotOperationsUseCase _playerSlotOperationsUseCase;
  final MatchLifecycleUseCase _matchLifecycleUseCase;
  final ConfigManagementUseCase _configManagementUseCase;

  
  // Removed unused _persistenceRepository field and _matchRepositories (now handled by RepositoryManagementUseCase)
  
  // Stream subscriptions for real-time updates
  final Map<String, StreamSubscription> _sourceSubscriptions = {};

  // WebSocket integration moved to WebSocketManagementUseCase
  
  // ========================================================================
  // CONSTRUCTOR
  // ========================================================================
  
  MatchManagementBloc(
    this._gameConfigUseCase,
    this._logger,
    this._playerSlotManagementUseCase,
    this._matchSourceManagementUseCase,
    this._matchDataSynchronizationUseCase,
    this._matchJoiningUseCase,
    this._webSocketManagementUseCase,
    this._serverConnectionUseCase,
    this._repositoryManagementUseCase,
    this._matchRefreshUseCase,
    this._playerSlotOperationsUseCase,
    this._matchLifecycleUseCase,
    this._configManagementUseCase,

  ) : super(const MatchManagementState()) {
    // Initialize event handlers
    _registerEventHandlers();

    // Don't initialize repositories in constructor - do it lazily when needed
    // This prevents dependency injection order issues
  }

  // ========================================================================
  // EVENT HANDLER REGISTRATION
  // ========================================================================
  
  void _registerEventHandlers() {
    // Initialization events
    on<InitializeMatchManagementEvent>(_onInitialize);
    on<LoadAvailableMatchSourcesEvent>(_onLoadAvailableMatchSources);
    
    // Match discovery & selection events
    on<LoadMatchDataEvent>(_onLoadMatchData);
    on<RefreshMatchesFromSourceEvent>(_onRefreshMatchesFromSource);
    on<SelectMatchEvent>(_onSelectMatch);
    on<ClearSelectedMatchEvent>(_onClearSelectedMatch);
    
    // Match joining events
    on<JoinSelectedMatchEvent>(_onJoinSelectedMatch);
    on<JoinMatchEvent>(_onJoinMatch);
    on<LeaveMatchEvent>(_onLeaveMatch);
    
    // Match creation events
    on<StartMatchCreationEvent>(_onStartMatchCreation);
    on<CancelMatchCreationEvent>(_onCancelMatchCreation);
    on<SelectMatchConfigEvent>(_onSelectMatchConfig);
    on<UpdateGameNameEvent>(_onUpdateGameName);
    
    // Player slot management events
    on<AddPlayerSlotEvent>(_onAddPlayerSlot);
    on<RemovePlayerSlotEvent>(_onRemovePlayerSlot);
    on<UpdatePlayerTypeEvent>(_onUpdatePlayerType);
    on<UpdateSelectedMatchPlayerTypeEvent>(_onUpdateSelectedMatchPlayerType);
    on<UpdatePlayerNameEvent>(_onUpdatePlayerName);
    on<SetHostPlayerEvent>(_onSetHostPlayer);
    on<JoinPlayerSlotEvent>(_onJoinPlayerSlot);
    on<JoinSelectedMatchSlotEvent>(_onJoinSelectedMatchSlot);
    
    // Match lifecycle events
    on<CreateAndStartMatchEvent>(_onCreateAndStartMatch);
    on<DeleteMatchEvent>(_onDeleteMatch);
    
    // Real-time monitoring events
    on<SubscribeToMatchUpdatesEvent>(_onSubscribeToMatchUpdates);
    on<UnsubscribeFromMatchUpdatesEvent>(_onUnsubscribeFromMatchUpdates);
    
    // Source management events
    on<AddMatchSourceEvent>(_onAddMatchSource);
    on<RemoveMatchSourceEvent>(_onRemoveMatchSource);
    
    // Network & capability events
    on<NetworkCapabilityChangedEvent>(_onNetworkCapabilityChanged);
    
    // UI state events
    on<ToggleServerProfileSelectorEvent>(_onToggleServerProfileSelector);

    // Internal update events
    on<HandleMatchUpdatesFromSourceEvent>(_onHandleMatchUpdatesFromSource);
    on<HandleWebSocketMatchUpdateEvent>(_onHandleWebSocketMatchUpdate);
    on<ServerScopeAvailableEvent>(_onServerScopeAvailable);
    on<ServerScopeLostEvent>(_onServerScopeLost);
  }

  // ========================================================================
  // REPOSITORY INITIALIZATION
  // ========================================================================


  


  // ========================================================================
  // INITIALIZATION EVENT HANDLERS
  // ========================================================================
  
  Future<void> _onInitialize(
    InitializeMatchManagementEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      _logger.info('Initializing MatchManagementBloc');

      // Initialize repositories
      final repoResult = await _matchSourceManagementUseCase.initializeRepositories();
      if (repoResult.isFailure) {
        emit(state.setError(repoResult.error));
        return;
      }

      // Load available game configurations
      final configs = await _gameConfigUseCase.loadLocalGameConfigs();

      final repoData = repoResult.value;
      emit(state.copyWith(
        availableConfigs: configs,
        availableMatchSources: repoData.availableSources,
        hasNetworkCapability: repoData.hasNetworkCapability,
        processingStatus: ProcessingStatus.loaded,
      ));

      _logger.info('MatchManagementBloc initialized with ${configs.length} configs and ${repoData.availableSources.length} sources');

    } catch (e) {
      _logger.error('Failed to initialize MatchManagementBloc: $e');
      emit(state.setError('Failed to initialize: $e'));
    }
  }

  Future<void> _onLoadAvailableMatchSources(
    LoadAvailableMatchSourcesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    try {
      final availableSources = _repositoryManagementUseCase.getAvailableSources();
      emit(state.copyWith(availableMatchSources: availableSources));
      _logger.info('Loaded ${availableSources.length} available match sources');
    } catch (e) {
      _logger.error('Failed to load available match sources: $e');
      emit(state.setError('Failed to load match sources: $e'));
    }
  }

  // ========================================================================
  // MATCH DISCOVERY & SELECTION EVENT HANDLERS
  // ========================================================================
  
  Future<void> _onLoadMatchData(
    LoadMatchDataEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = LoadMatchDataRequest(
      gameName: event.gameName,
      forceRefresh: false,
    );

    final result = await _matchDataSynchronizationUseCase.loadMatchData(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (matchData) {
        emit(state.copyWith(
          openMatches: matchData.allMatches,
          matchesBySource: matchData.matchesBySource,
          availableMatchSources: matchData.availableSources,
          hasNetworkCapability: matchData.hasNetworkCapability,
          processingStatus: ProcessingStatus.loaded,
        ));
      },
    );
  }

  Future<void> _onRefreshMatchesFromSource(
    RefreshMatchesFromSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final request = RefreshMatchesFromSourceRequest(
      sourceName: event.sourceName,
      currentMatchesBySource: state.matchesBySource,
      gameName: state.gameName,
    );

    final result = await _matchRefreshUseCase.refreshMatchesFromSource(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (refreshResult) {
        emit(state.copyWith(
          openMatches: refreshResult.allMatches,
          matchesBySource: refreshResult.updatedMatchesBySource,
          errorMessage: null, // Clear any previous errors on successful refresh
        ));

        _logger.info('Refreshed ${refreshResult.refreshedMatches.length} matches from ${refreshResult.sourceName}');
      },
    );
  }

  void _onSelectMatch(
    SelectMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Match selected: ${event.match.id}');
    
    emit(state.copyWith(
      selectedMatch: event.match,
      currentMode: 'selection',
    ));
  }

  void _onClearSelectedMatch(
    ClearSelectedMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Clearing selected match: ${event.matchId ?? 'current'}');
    
    emit(state.copyWith(
      selectedMatch: null,
    ));
  }

  // ========================================================================
  // MATCH JOINING EVENT HANDLERS
  // ========================================================================

  Future<void> _onJoinSelectedMatch(
    JoinSelectedMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (state.selectedMatch == null) {
      emit(state.setError('No match selected'));
      return;
    }

    emit(state.setLoading());

    final request = JoinMatchRequest(matchId: state.selectedMatch!.id);
    final result = await _matchJoiningUseCase.joinMatch(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (joinResult) {
        _logger.info('Successfully joined match ${joinResult.matchId}');
        emit(state.setLoaded());
      },
    );
  }

  Future<void> _onJoinMatch(
    JoinMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = JoinMatchRequest(
      matchId: event.matchId,
      playerId: event.playerId,
    );
    final result = await _matchJoiningUseCase.joinMatch(request);

    result.fold(
      (error) {
        _logger.error('❌ JOIN FAILED: Could not join match ${event.matchId}: $error');
        emit(state.setError('Failed to join match: $error'));
      },
      (joinResult) {
        _logger.info('✅ JOIN SUCCESS: Joined match ${event.matchId}, waiting for WebSocket update...');
        emit(state.setLoaded());
      },
    );
  }

  Future<void> _onLeaveMatch(
    LeaveMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = LeaveMatchRequest(
      matchId: event.matchId,
      playerId: event.playerId,
    );
    final result = await _matchJoiningUseCase.leaveMatch(request);

    result.fold(
      (error) => emit(state.setError('Failed to leave match: $error')),
      (leaveResult) {
        _logger.info('Successfully left match ${event.matchId}');
        emit(state.setLoaded());
      },
    );
  }

  // ========================================================================
  // MATCH CREATION EVENT HANDLERS
  // ========================================================================

  void _onStartMatchCreation(
    StartMatchCreationEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Starting match creation');

    final result = _configManagementUseCase.getOrCreateDefaultConfig(state.availableConfigs);

    result.fold(
      (error) {
        _logger.error('Failed to get default config: $error');
        emit(state.setError(error));
      },
      (defaultResult) {
        emit(state.enterCreationMode().copyWith(
          selectedConfigId: defaultResult.config.id,
          gameName: defaultResult.config.name,
          matchId: null,
          playerSlots: defaultResult.defaultSlots,
        ));
      },
    );
  }

  void _onCancelMatchCreation(
    CancelMatchCreationEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Cancelling match creation');

    emit(state.enterSelectionMode().copyWith(
      selectedConfigId: null,
      gameName: null,
      matchId: null,
      playerSlots: [],
    ));
  }

  void _onSelectMatchConfig(
    SelectMatchConfigEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Selected match config: ${event.matchConfigId}');

    final validationResult = _configManagementUseCase.validateConfigSelection(
      event.matchConfigId,
      state.availableConfigs,
    );

    validationResult.fold(
      (error) {
        _logger.warn('Invalid config selection: $error');
        emit(state.setError(error));
      },
      (isValid) {
        if (isValid) {
          // Handle configuration change to update slots if needed
          final changeResult = _configManagementUseCase.handleConfigurationChange(
            event.matchConfigId,
            state.availableConfigs,
            state.playerSlots,
          );

          changeResult.fold(
            (error) => emit(state.setError(error)),
            (changeData) {
              emit(state.copyWith(
                selectedConfigId: event.matchConfigId,
                playerSlots: changeData.requiresSlotReset ? changeData.updatedSlots : state.playerSlots,
              ));
            },
          );
        }
      },
    );
  }

  void _onUpdateGameName(
    UpdateGameNameEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    emit(state.copyWith(gameName: event.gameName));
  }

  // ========================================================================
  // PLAYER SLOT MANAGEMENT EVENT HANDLERS
  // ========================================================================

  void _onAddPlayerSlot(
    AddPlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // Get the current config using ConfigManagementUseCase
    GameConfig? config;

    if (state.selectedConfigId != null) {
      final configResult = _configManagementUseCase.getConfigById(state.selectedConfigId!, state.availableConfigs);
      if (configResult.isFailure) {
        emit(state.setError('Failed to get config: ${configResult.error}'));
        return;
      }
      config = configResult.value;
    }

    if (config == null) {
      final fallbackResult = _configManagementUseCase.createFallbackConfig();
      if (fallbackResult.isFailure) {
        emit(state.setError('Failed to create fallback config: ${fallbackResult.error}'));
        return;
      }
      config = fallbackResult.value;
    }

    final result = _playerSlotManagementUseCase.addPlayerSlot(state.playerSlots, config);

    if (result.isSuccess) {
      emit(state.copyWith(playerSlots: result.value));
      _logger.info('Added player slot successfully');
    } else {
      emit(state.setError(result.error));
      _logger.warn('Failed to add player slot: ${result.error}');
    }
  }

  void _onRemovePlayerSlot(
    RemovePlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final result = _playerSlotManagementUseCase.removePlayerSlot(state.playerSlots, event.slotIndex);

    if (result.isSuccess) {
      emit(state.copyWith(playerSlots: result.value));
      _logger.info('Removed player slot at index ${event.slotIndex}');
    } else {
      emit(state.setError(result.error));
      _logger.warn('Failed to remove player slot: ${result.error}');
    }
  }

  Future<void> _onUpdatePlayerType(
    UpdatePlayerTypeEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = UpdatePlayerTypeRequest(
      slotIndex: event.slotIndex,
      newType: event.playerType,
      currentSlots: state.playerSlots,
      gameConfig: state.selectedConfig!,
      matchId: state.matchId,
      isSelectedMatch: false,
    );

    final result = await _playerSlotManagementUseCase.updatePlayerType(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (updateResult) {
        emit(state.copyWith(
          playerSlots: updateResult.updatedSlots,
          processingStatus: ProcessingStatus.loaded,
        ));

        // If a network match was created, update the state but rely on WebSocket for data updates
        if (updateResult.requiresNetworkMatch && updateResult.updatedMatch != null) {
          emit(state.copyWith(
            matchId: updateResult.updatedMatch!.id,
            selectedMatch: updateResult.updatedMatch,
          ));
          // Note: No manual data reload - WebSocket will broadcast the new match to all subscribers
          _logger.info('Network match created: ${updateResult.updatedMatch!.id} - waiting for WebSocket update');
        }
      },
    );
  }

  Future<void> _onUpdateSelectedMatchPlayerType(
    UpdateSelectedMatchPlayerTypeEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final selectedMatch = state.selectedMatch;
    if (selectedMatch == null) {
      _logger.warn('No selected match to update player type');
      return;
    }

    emit(state.copyWith(processingStatus: ProcessingStatus.loading));

    final request = UpdateSelectedMatchPlayerTypeRequest(
      selectedMatch: selectedMatch,
      slotIndex: event.slotIndex,
      playerType: event.playerType,
      matchesBySource: state.matchesBySource,
    );

    final result = await _playerSlotOperationsUseCase.updateSelectedMatchPlayerType(request);

    result.fold(
      (error) {
        _logger.error('Failed to update player type: $error');
        emit(state.copyWith(
          processingStatus: ProcessingStatus.error,
          errorMessage: error,
        ));
      },
      (updateResult) {
        if (updateResult.serverUpdateSuccessful || !updateResult.requiresServerUpdate) {
          emit(state.copyWith(
            selectedMatch: updateResult.updatedMatch,
            processingStatus: ProcessingStatus.loaded,
            errorMessage: null,
          ));
        } else {
          // Keep the local update but show error status
          emit(state.copyWith(
            selectedMatch: updateResult.updatedMatch,
            processingStatus: ProcessingStatus.error,
            errorMessage: updateResult.errorMessage ?? 'Failed to update server match',
          ));
        }
      },
    );
  }

  void _onUpdatePlayerName(
    UpdatePlayerNameEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final request = UpdatePlayerNameRequest(
      currentSlots: state.playerSlots,
      slotIndex: event.slotIndex,
      name: event.name,
    );

    final result = _playerSlotOperationsUseCase.updatePlayerName(request);

    result.fold(
      (error) {
        _logger.warn('Failed to update player name: $error');
        emit(state.setError(error));
      },
      (updatedSlots) {
        emit(state.copyWith(playerSlots: updatedSlots));
        _logger.info('Updated player name for slot ${event.slotIndex}: ${event.name}');
      },
    );
  }

  void _onSetHostPlayer(
    SetHostPlayerEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // For now, we'll track the host by keeping the host slot as the first slot
    // or by using a separate hostPlayerId field in the state
    // Since PlayerSlot doesn't have isHost, we'll handle this in the UI layer

    _logger.info('Set host player: ${event.slotId}');

    // TODO: Implement host tracking logic when PlayerSlot model supports it
    // For now, just log the event
  }

  void _onJoinPlayerSlot(
    JoinPlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final userInfoResult = _playerSlotOperationsUseCase.getCurrentUserInfo();
    if (userInfoResult.isFailure) {
      emit(state.setError(userInfoResult.error));
      return;
    }

    final userInfo = userInfoResult.value;
    final request = JoinPlayerSlotRequest(
      currentSlots: state.playerSlots,
      slotIndex: event.slotIndex,
      userId: userInfo.id,
      userName: userInfo.name,
    );

    final result = _playerSlotOperationsUseCase.joinPlayerSlot(request);

    result.fold(
      (error) {
        _logger.warn('Failed to join player slot: $error');
        emit(state.setError(error));
      },
      (updatedSlots) {
        emit(state.copyWith(playerSlots: updatedSlots));
        _logger.info('User ${userInfo.id} joined slot ${event.slotIndex}');
      },
    );
  }

  /// Join the current user to a specific slot in the selected match (with server update)
  Future<void> _onJoinSelectedMatchSlot(
    JoinSelectedMatchSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (state.selectedMatch == null) {
      emit(state.setError('No match selected'));
      return;
    }

    final user = GetIt.I<UserManager>().state.user;
    if (user == null) {
      emit(state.setError('No user logged in'));
      return;
    }

    final selectedMatch = state.selectedMatch!;
    final currentUserId = user.id;

    emit(state.setLoading());

    // Check if this is a network match
    final isNetworkMatch = _matchJoiningUseCase.isNetworkMatch(selectedMatch, state.matchesBySource);

    final request = JoinMatchSlotRequest(
      matchId: selectedMatch.id,
      slotIndex: event.slotIndex,
      playerId: currentUserId,
      currentSlots: selectedMatch.playerSlots,
      isNetworkMatch: isNetworkMatch,
    );

    final result = await _matchJoiningUseCase.joinMatchSlot(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (joinResult) {
        if (isNetworkMatch) {
          // For network matches, wait for WebSocket update
          _logger.info('✅ SLOT JOIN SUCCESS: Joined match ${selectedMatch.id} slot ${event.slotIndex}, waiting for WebSocket update...');
          emit(state.setLoaded());
        } else {
          // For local matches, update local state immediately
          final switchResult = _matchJoiningUseCase.switchPlayerSlot(SwitchSlotRequest(
            matchId: selectedMatch.id,
            playerId: currentUserId,
            fromSlotIndex: -1, // Find and remove from any existing slot
            toSlotIndex: event.slotIndex,
            currentSlots: selectedMatch.playerSlots,
          ));

          switchResult.fold(
            (error) => emit(state.setError(error)),
            (slotResult) {
              final updatedMatch = selectedMatch.copyWith(playerSlots: slotResult.updatedSlots);
              emit(state.copyWith(
                selectedMatch: updatedMatch,
                processingStatus: ProcessingStatus.loaded,
              ));
              _logger.info('User $currentUserId joined slot ${event.slotIndex} in local match');
            },
          );
        }
      },
    );
  }

  // ========================================================================
  // MATCH LIFECYCLE EVENT HANDLERS
  // ========================================================================

  Future<void> _onCreateAndStartMatch(
    CreateAndStartMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = lifecycle.CreateAndStartMatchRequest(
      gameConfig: state.selectedConfig!,
      playerSlots: state.playerSlots,
      gameName: state.gameName ?? '',
      openForJoining: event.openForJoining,
    );

    final result = await _matchLifecycleUseCase.createAndStartMatch(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (creationResult) {
        emit(state.copyWith(
          matchId: creationResult.createdMatch.id,
          selectedMatch: creationResult.shouldSelectMatch ? creationResult.createdMatch : null,
          processingStatus: ProcessingStatus.loaded,
        ));

        // Note: No manual data refresh - WebSocket will broadcast the new match to all subscribers
        if (creationResult.requiresDataRefresh) {
          _logger.info('Match creation completed - waiting for WebSocket update');
        }
      },
    );
  }

  Future<void> _onDeleteMatch(
    DeleteMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = lifecycle.DeleteMatchRequest(matchId: event.matchId);
    final result = await _matchLifecycleUseCase.deleteMatch(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (deletionResult) {
        _logger.info('Successfully deleted match: ${deletionResult.deletedMatchId}');

        // Clear selected match if it was deleted
        if (deletionResult.shouldClearSelection) {
          emit(state.copyWith(selectedMatch: null));
        }

        // Note: No manual data refresh - WebSocket will broadcast the match deletion to all subscribers
        if (deletionResult.requiresDataRefresh) {
          _logger.info('Match deletion completed - waiting for WebSocket update');
        }

        emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
      },
    );
  }

  // ========================================================================
  // REAL-TIME MONITORING EVENT HANDLERS
  // ========================================================================

  Future<void> _onSubscribeToMatchUpdates(
    SubscribeToMatchUpdatesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final request = SubscribeToRealTimeUpdatesRequest();
    final result = await _matchDataSynchronizationUseCase.subscribeToRealTimeUpdates(request);

    result.fold(
      (error) {
        _logger.error('Error subscribing to match updates: $error');
        emit(state.setError('Error subscribing to updates: $error'));
      },
      (subscriptionResult) {
        emit(state.copyWith(
          isSubscribedToUpdates: true,
          monitoredSources: state.availableMatchSources.toSet(),
        ));
        _logger.info('Successfully subscribed to real-time match updates');

        // Set up BLoC-level WebSocket listener using the use case
        _setupWebSocketListenerViaUseCase();
      },
    );
  }

  Future<void> _onUnsubscribeFromMatchUpdates(
    UnsubscribeFromMatchUpdatesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final request = UnsubscribeFromRealTimeUpdatesRequest();
    final result = await _matchDataSynchronizationUseCase.unsubscribeFromRealTimeUpdates(request);

    result.fold(
      (error) {
        _logger.error('Error unsubscribing from match updates: $error');
      },
      (unsubscribeResult) {
        // Tear down WebSocket listener using the use case
        _webSocketManagementUseCase.teardownWebSocketListener();

        emit(state.copyWith(
          isSubscribedToUpdates: false,
          monitoredSources: {},
        ));
        _logger.info('Successfully unsubscribed from real-time match updates');
      },
    );
  }

  // ========================================================================
  // SOURCE MANAGEMENT EVENT HANDLERS
  // ========================================================================

  void _onAddMatchSource(
    AddMatchSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final updatedSources = [...state.availableMatchSources];
    if (!updatedSources.contains(event.sourceName)) {
      updatedSources.add(event.sourceName);
      emit(state.copyWith(availableMatchSources: updatedSources));
      _logger.info('Added match source: ${event.sourceName}');
    }
  }

  Future<void> _onRemoveMatchSource(
    RemoveMatchSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final sourceName = event.sourceName;
    _logger.info('Removing match source: $sourceName');

    final request = RemoveMatchSourceRequest(sourceName: sourceName);
    final result = await _matchSourceManagementUseCase.removeMatchSource(request);

    result.fold(
      (error) {
        _logger.error('Failed to remove match source: $error');
        emit(state.setError(error));
      },
      (removeResult) {
        // Update available sources
        final updatedSources = [...state.availableMatchSources];
        updatedSources.remove(sourceName);

        // Remove matches from this source
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
        updatedMatchesBySource.remove(sourceName);

        // Check current network capability
        final hasNetworkCapability = _matchSourceManagementUseCase.hasNetworkCapability();

        // Update state
        emit(state.copyWith(
          availableMatchSources: updatedSources,
          matchesBySource: updatedMatchesBySource,
          hasNetworkCapability: hasNetworkCapability,
        ));

        _logger.info('Removed match source: $sourceName, remaining sources: $updatedSources');

        // Only disconnect if we truly lost network capability due to actual network issues
        // Don't disconnect during normal app lifecycle changes (background/foreground)
        if (!hasNetworkCapability) {
          _logger.info('Network capability lost - checking if this is due to app lifecycle or actual network loss');

          // TODO: Add more intelligent detection here
          // For now, we'll be more conservative and only disconnect on explicit user action
          // _serverConnectionUseCase.disconnectFromServer();
          // _webSocketManagementUseCase.teardownWebSocketListener();

          _logger.info('Keeping WebSocket connection active during scope loss to maintain real-time updates');
        }
      },
    );
  }

  // ========================================================================
  // NETWORK & CAPABILITY EVENT HANDLERS
  // ========================================================================

  void _onNetworkCapabilityChanged(
    NetworkCapabilityChangedEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // Check if we're losing network capability
    bool shouldClearMatchCreation = false;
    bool shouldClearSelectedMatch = false;

    if (!event.hasNetworkCapability && state.hasNetworkCapability) {
      // Check if currently creating a network match
      if (state.isCreatingMatch) {
        final hasNetworkPlayers = state.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (hasNetworkPlayers) {
          _logger.info('Clearing network match creation due to network capability loss');
          shouldClearMatchCreation = true;
        }
      }

      // Check if we have a selected network match for joining
      if (state.selectedMatch != null) {
        final selectedMatchHasNetworkPlayers = state.selectedMatch!.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (selectedMatchHasNetworkPlayers) {
          _logger.info('Clearing selected network match due to network capability loss');
          shouldClearSelectedMatch = true;
        }
      }
    }

    if (shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        hasNetworkCapability: event.hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        selectedMatch: shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        hasNetworkCapability: event.hasNetworkCapability,
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(hasNetworkCapability: event.hasNetworkCapability));
    }

    _logger.info('Network capability changed: ${event.hasNetworkCapability}');
  }

  // ========================================================================
  // UI STATE EVENT HANDLERS
  // ========================================================================

  void _onToggleServerProfileSelector(
    ToggleServerProfileSelectorEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final expanded = event.expanded ?? !state.serverProfileSelectorExpanded;
    emit(state.copyWith(serverProfileSelectorExpanded: expanded));
  }

  // ========================================================================
  // HELPER METHODS
  // ========================================================================



  /// Handle match updates from a specific source
  void _onHandleMatchUpdatesFromSource(
    HandleMatchUpdatesFromSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
    updatedMatchesBySource[event.sourceName] = event.matches;

    // Rebuild the complete matches list
    final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

    emit(state.copyWith(
      openMatches: allMatches,
      matchesBySource: updatedMatchesBySource,
    ));

    _logger.info('Received ${event.matches.length} match updates from ${event.sourceName}');
  }

  // ========================================================================
  // NETWORK SOURCE INTEGRATION
  // ========================================================================





  /// Re-initialize repositories when server scope becomes available
  /// This should be called when the server connection is established
  Future<void> _onServerScopeAvailable(
    ServerScopeAvailableEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final result = await _serverConnectionUseCase.handleServerScopeAvailable();

    result.fold(
      (error) {
        _logger.error('Error handling server scope available: $error');
        emit(state.setError('Failed to connect to server: $error'));
      },
      (initResult) {
        emit(state.copyWith(
          availableMatchSources: initResult.availableSources,
          hasNetworkCapability: initResult.hasNetworkCapability,
        ));

        _logger.info('Updated match sources after server scope activation: ${initResult.availableSources}');

        // Subscribe to WebSocket updates when network becomes available
        if (initResult.hasNetworkCapability) {
          add(const SubscribeToMatchUpdatesEvent());
        }

        // Load initial matches from newly available sources (one-time only)
        // After this, all updates will come via WebSocket
        add(const LoadMatchDataEvent());
      },
    );
  }

  /// Public method to trigger server scope available event
  void onServerScopeAvailable() {
    add(const ServerScopeAvailableEvent());
  }

  /// Clean up when server scope is lost
  /// This should be called when the server connection is lost
  Future<void> _onServerScopeLost(
    ServerScopeLostEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final scopeResult = await _serverConnectionUseCase.handleServerScopeLost();

    scopeResult.fold(
      (error) {
        _logger.error('Error handling server scope lost: $error');
      },
      (initResult) {
        // Evaluate network state to determine cleanup actions
        final networkStateResult = _serverConnectionUseCase.evaluateNetworkState(
          initResult.hasNetworkCapability,
          state.isCreatingMatch,
          state.playerSlots,
          state.selectedMatch,
        );

        networkStateResult.fold(
          (error) {
            _logger.error('Error evaluating network state: $error');
          },
          (networkState) {
            _handleNetworkStateCleanup(emit, initResult, networkState);
          },
        );
      },
    );

    // Unsubscribe from WebSocket updates
    _webSocketManagementUseCase.teardownWebSocketListener();
  }

  void _handleNetworkStateCleanup(
    Emitter<MatchManagementState> emit,
    results.RepositoryInitializationResult initResult,
    NetworkStateResult networkState,
  ) {
    if (networkState.shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        availableMatchSources: initResult.availableSources,
        hasNetworkCapability: initResult.hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
        selectedMatch: networkState.shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (networkState.shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        availableMatchSources: initResult.availableSources,
        hasNetworkCapability: initResult.hasNetworkCapability,
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(
        availableMatchSources: initResult.availableSources,
        hasNetworkCapability: initResult.hasNetworkCapability,
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
      ));
    }

    _logger.info('Server scope cleanup completed, remaining sources: ${initResult.availableSources}');
  }

  /// Public method to trigger server scope lost event
  void onServerScopeLost() {
    add(const ServerScopeLostEvent());
  }

  /// Remove a network match source when server connection is lost
  void removeNetworkSource(String sourceName) {
    // Use the existing RemoveMatchSourceEvent to handle this properly
    add(RemoveMatchSourceEvent(sourceName));
  }

  /// Set up WebSocket listener using the WebSocketManagementUseCase
  Future<void> _setupWebSocketListenerViaUseCase() async {
    final result = await _webSocketManagementUseCase.setupWebSocketListener();

    result.fold(
      (error) {
        _logger.error('Failed to set up WebSocket listener via use case: $error');
      },
      (subscription) {
        // Subscription is managed by the WebSocketManagementUseCase
        _logger.info('WebSocket listener set up successfully via use case');
      },
    );
  }

  // ========================================================================
  // WEBSOCKET INTEGRATION
  // ========================================================================





  /// Handle WebSocket match updates
  void _onHandleWebSocketMatchUpdate(
    HandleWebSocketMatchUpdateEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final result = _webSocketManagementUseCase.processWebSocketUpdate(
      event.matches,
      state.matchesBySource,
      state.selectedMatch,
    );

    result.fold(
      (error) {
        _logger.error('Failed to handle WebSocket match update: $error');
      },
      (updateResult) {
        emit(state.copyWith(
          openMatches: updateResult.updatedMatches,
          matchesBySource: state.matchesBySource.map((key, value) =>
            key == updateResult.sourceName
                ? MapEntry(key, event.matches)
                : MapEntry(key, value)
          ),
          selectedMatch: _webSocketManagementUseCase.updateSelectedMatchFromWebSocket(
            event.matches,
            state.selectedMatch,
          ),
        ));

        _logger.info('🔄 WEBSOCKET APPLIED: Updated ${event.matches.length} matches from ${updateResult.sourceName}');
        _logger.info('📊 TOTAL MATCHES: ${updateResult.updatedMatches.length} matches now available');
      },
    );
  }

  // ========================================================================
  // NETWORK MATCH CREATION HELPERS
  // ========================================================================









  // ========================================================================
  // CLEANUP
  // ========================================================================

  @override
  Future<void> close() async {
    // Cancel all stream subscriptions
    for (final subscription in _sourceSubscriptions.values) {
      await subscription.cancel();
    }
    _sourceSubscriptions.clear();

    // Cancel WebSocket subscription
    _webSocketManagementUseCase.teardownWebSocketListener();

    _logger.info('MatchManagementBloc closed');
    return super.close();
  }
}


