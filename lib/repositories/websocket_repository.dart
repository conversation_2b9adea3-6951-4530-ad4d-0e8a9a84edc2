import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:common/models/game_match.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/models/base/game_match.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// Abstract repository for HTTP requests that can be used as fallback
/// when WebSocket connections are interrupted
abstract class HttpRepository {
  /// Fetch open matches
  Future<List<GameMatch>> fetchOpenMatches();
  
  /// Fetch details for a specific match
  Future<GameMatch?> fetchMatchDetails(String matchId);
}

/// Global error trap to prevent unhandled WebSocket exceptions
void handlePotentialWebSocketError(Function callback) {
  try {
    callback();
  } catch (e) {
    print('WebSocketRepository GLOBAL: Caught potential uncaught exception: $e');
    // Log stack trace to help debug the source
    print('WebSocketRepository GLOBAL: Stack trace:\n${StackTrace.current}');
  }
}

/// Global guard against unhandled socket exceptions
Future<T?> runWithSocketExceptionGuard<T>(T Function() callback) async {
  try {
    // Run the callback in a special error zone that catches WebSocketChannelException
    return runZonedGuarded(
      callback,
      (error, stackTrace) {
        if (error.toString().contains('WebSocketChannelException') || 
            error.toString().contains('SocketException')) {
          print('WebSocketRepository ZONE GUARD: Caught unhandled socket exception: $error');
          print('WebSocketRepository ZONE GUARD: Stack trace:\n$stackTrace');
          // Don't rethrow - this is what prevents the app from crashing
        } else {
          // For other exceptions, rethrow to be handled by regular error handling
          Zone.current.handleUncaughtError(error, stackTrace);
        }
      },
    );
  } catch (e) {
    print('WebSocketRepository GUARD: Caught exception in guard: $e');
    // Return a default value or rethrow based on your needs
    rethrow;
  }
}

/// Repository for WebSocket connections
@singleton
class WebSocketRepository {
  /// WebSocket channel
  WebSocketChannel? _channel;

  /// Stream controller for game_match updates
  final _matchUpdateController = StreamController<GameMatch>.broadcast();

  /// Stream of game_match updates
  Stream<GameMatch> get matchUpdates => _matchUpdateController.stream;

  /// Stream controller for connection status updates
  final _connectionStatusController = StreamController<bool>.broadcast();

  /// Stream of connection status updates
  Stream<bool> get connectionStatusUpdates =>
      _connectionStatusController.stream;

  /// Get connection status
  bool get isConnected => _isConnected;

  /// Get connection health status
  bool get isHealthy => _isHealthy;

  /// Get last successful connection time
  DateTime? get lastSuccessfulConnection => _lastSuccessfulConnection;

  /// Get time since last successful connection
  Duration? get timeSinceLastConnection {
    if (_lastSuccessfulConnection == null) return null;
    return DateTime.now().difference(_lastSuccessfulConnection!);
  }
  
  /// Get connection status stream
  Stream<bool> get connectionStatus => _connectionStatusController.stream;
  
  /// Check if already subscribed to a specific topic
  bool isSubscribedToTopic(String topic) => _activeSubscriptions.contains(topic);
  
  /// Check if already subscribed to open matches
  bool get isSubscribedToOpenMatches => isSubscribedToTopic('open_matches');
  
  /// Get stream for a specific topic
  Stream<dynamic> getTopicStream(String topic) {
    try {
      // Create a controller for this topic if it doesn't exist
      _topicControllers[topic] ??= StreamController.broadcast();
      return _topicControllers[topic]!.stream;
    } catch (e) {
      print('WebSocketRepository: Error getting topic stream: $e');
      // Create a fallback controller if there was an issue
      final fallbackController = StreamController<dynamic>.broadcast();
      return fallbackController.stream;
    }
  }
  
  /// Check if a topic has active listeners (for debugging)
  bool hasTopicListeners(String topic) {
    try {
      final controller = _topicControllers[topic];
      return controller?.hasListener ?? false;
    } catch (e) {
      print('WebSocketRepository: Error checking topic listeners: $e');
      return false;
    }
  }
  
  /// Get topic subscription info for debugging
  Map<String, bool> getTopicDebugInfo() {
    final result = <String, bool>{};
    for (final topic in _topicControllers.keys) {
      result[topic] = _topicControllers[topic]?.hasListener ?? false;
    }
    return result;
  }
  
  /// Stream controller for topic-based streams
  final Map<String, StreamController> _topicControllers = {};
  
  /// Set of active topic subscriptions
  final Set<String> _activeSubscriptions = {};

  /// Map of game_match factories for deserializing game_match updates
  final Map<String, Function(Map<String, dynamic>)> _matchFactories = {};

  // No legacy flags needed

  /// Connection status
  bool _isConnected = false;

  /// Lock to prevent multiple simultaneous connection attempts
  bool _isConnecting = false;

  /// Constructor
  WebSocketRepository();

  /// Update the connection status and notify listeners
  void _updateConnectionStatus(bool status) {
    _isConnected = status;
    _connectionStatusController.add(status);

    // Update health monitoring
    if (status) {
      _lastSuccessfulConnection = DateTime.now();
      _reconnectAttempts = 0; // Reset attempts on successful connection
      _isHealthy = true;
      print('WebSocketRepository: Connection healthy - last successful: $_lastSuccessfulConnection');
    } else {
      _isHealthy = false;
      print('WebSocketRepository: Connection unhealthy - last successful: $_lastSuccessfulConnection');
    }
  }

  /// WebSocket connection check timer
  Timer? _reconnectTimer;

  /// Track reconnection attempts
  int _reconnectAttempts = 0;

  /// Maximum number of reconnect attempts before using longer delays
  static const int maxReconnectAttempts = 5;

  /// Track last successful connection time for health monitoring
  DateTime? _lastSuccessfulConnection;

  /// Track connection health status
  bool _isHealthy = true;

  /// Minimum time between connection attempts to avoid aggressive reconnection
  static const Duration _minReconnectInterval = Duration(seconds: 2);

  /// Handle WebSocket disconnection
  void _handleDisconnect() {
    _updateConnectionStatus(false);

    // Only schedule reconnect if this seems like a real network issue
    if (_shouldAttemptReconnect()) {
      _scheduleReconnect();
    } else {
      print('WebSocketRepository: Skipping reconnect - likely app lifecycle change');
    }
  }

  /// Determine if we should attempt to reconnect based on connection health
  bool _shouldAttemptReconnect() {
    // Always attempt reconnect if we've never connected successfully
    if (_lastSuccessfulConnection == null) {
      return true;
    }

    // If we were connected recently (within last 30 seconds), this might be app lifecycle
    final timeSinceLastConnection = DateTime.now().difference(_lastSuccessfulConnection!);
    if (timeSinceLastConnection < const Duration(seconds: 30)) {
      // For macOS desktop apps, be more conservative about reconnecting
      // This helps avoid unnecessary reconnections during window focus changes
      print('WebSocketRepository: Recent connection (${timeSinceLastConnection.inSeconds}s ago) - being conservative about reconnect');
      return false;
    }

    // If it's been a while, this is likely a real network issue
    return true;
  }

  /// Connect to the WebSocket server
  Future<void> connect() async {
    await runWithSocketExceptionGuard(() async {
      if (_isConnected) {
        return;
      }
      
      // Don't allow multiple simultaneous connection attempts
      if (_isConnecting) {
        return;
      }
      
      _isConnecting = true;
      
      try {
        // Initialize WebSocket connection
        final wsUrl = GetIt.I<ServerEnvironmentManager>().state.profile?.wsUrl;

        if (wsUrl == null) {
          // Silently fail when no server is configured - this is expected in local development
          _isConnecting = false;
          _isConnected = false;
          return;
        }
        
        try {
          _channel = IOWebSocketChannel.connect(wsUrl);
        } catch (socketError) {
          _isConnecting = false;
          rethrow;
        }
        
        // Set up listener for messages
        _channel!.stream.listen(
          (dynamic message) {
            _handleMessage(message);
          },
          onError: (error) {
            _handleError(error);
          },
          onDone: () {
            _handleDisconnect();
          },
          cancelOnError: false,
        );
        
        // Mark as connected
        _updateConnectionStatus(true);
        _isConnecting = false;
        
        // Resubscribe to active topics
        _resubscribeToActiveTopics();
      } catch (error) {
        _isConnecting = false;
        _updateConnectionStatus(false);
        rethrow;
      }
    });
  }

  /// Safely resubscribe to all topics after reconnection
  void _resubscribeToActiveTopics() {
    try {
      final topics = Set<String>.from(_activeSubscriptions);
      _activeSubscriptions.clear();
      for (final topic in topics) {
        subscribeToTopic(topic);
      }
    } catch (e) {
      print('WebSocketRepository: Error resubscribing to topics: $e');
    }
  }



  /// Disconnect from the WebSocket server
  void disconnect() {
    try {
      // Safely close the channel if it exists
      if (_channel != null) {
        try {
          _channel?.sink.close();
        } catch (e) {
          print('WebSocketRepository: Error closing WebSocket sink: $e');
          // Continue with disconnection process even if sink close fails
        }
      }
      
      _updateConnectionStatus(false);
      _reconnectTimer?.cancel();
      _reconnectTimer = null;
    } catch (e) {
      print('WebSocketRepository: Error during disconnect: $e');
      // Ensure connection status is updated even if there's an error
      _updateConnectionStatus(false);
    }
  }

  /// Register a factory function for a specific game_match type
  void registerMatchFactory(
      String matchType, GameMatch Function(Map<String, dynamic>) factory) {
    _matchFactories[matchType] = factory;
  }

  /// Handle WebSocket errors
  void _handleError(dynamic error) {
    // Handle connection error
    _updateConnectionStatus(false);
    _scheduleReconnect();
  }
  
  /// Handle messages from the WebSocket server
  void _handleMessage(dynamic message) {
    print('WebSocketRepository: 📥 Received message: $message');
    if (message is! String) {
      print('WebSocketRepository: ⚠️ Received non-string message: $message');
      return;
    }

    try {
      print('WebSocketRepository: 📥 Received raw message: $message');
      final data = jsonDecode(message) as Map<String, dynamic>;
      print('WebSocketRepository: 🔄 Parsed message: $data');
      
      // Handle messages with topic field (standard format)
      final topic = data['topic'] as String?;
      print('WebSocketRepository: 📌 Message topic: $topic');
      
      if (topic != null) {
        // Check if we're subscribed to this topic
        final isSubscribed = _activeSubscriptions.contains(topic);
        print('WebSocketRepository: 🔔 Subscribed to "$topic": $isSubscribed');
        
        if (isSubscribed) {
          // Get the controller for this topic
          final controller = _topicControllers[topic];
          final hasListeners = controller?.hasListener ?? false;
          print('WebSocketRepository: 👂 Topic "$topic" has listeners: $hasListeners');
          
          if (controller != null && hasListeners) {
            controller.add(data);
            print('WebSocketRepository: ✅ Dispatched message to topic "$topic"');
          } else {
            print('WebSocketRepository: ❌ No active controller or listeners for topic "$topic"');
          }
        } else {
          print('WebSocketRepository: ❌ Not subscribed to topic "$topic", message ignored');
        }
        
        // Special handling for subscription_success messages
        if (topic == 'subscription_success') {
          final subscribedTopic = data['data']?['topic'] as String?;
          if (subscribedTopic != null) {
            print('WebSocketRepository: Successfully subscribed to topic: $subscribedTopic');
          }
        }
      } else {
        print('WebSocketRepository: Message has no topic field: $data');
      }
    } catch (e) {
      print('Error parsing WebSocket message: $e');
    }
  }

  /// Schedule reconnection attempt with intelligent backoff
  void _scheduleReconnect() {
    // Cancel any existing timer
    _reconnectTimer?.cancel();

    // Calculate delay with exponential backoff, but be more conservative for desktop apps
    final baseDelay = _reconnectAttempts < 3 ? 5 : 15; // Start with longer delays
    final delay = Duration(seconds: math.min(60, baseDelay + _reconnectAttempts * 5));
    _reconnectAttempts++;

    print('WebSocketRepository: Scheduling reconnect attempt #$_reconnectAttempts in ${delay.inSeconds}s');

    // Schedule reconnection
    _reconnectTimer = Timer(delay, () {
      try {
        // Check if WebSocket URL is available before attempting to reconnect
        final wsUrl = GetIt.I<ServerEnvironmentManager>().state.profile?.wsUrl;
        if (wsUrl == null) {
          print('WebSocketRepository: No WebSocket URL available, stopping reconnection attempts');
          return;
        }

        // Check if enough time has passed since last attempt
        if (_lastSuccessfulConnection != null) {
          final timeSinceLastConnection = DateTime.now().difference(_lastSuccessfulConnection!);
          if (timeSinceLastConnection < _minReconnectInterval) {
            print('WebSocketRepository: Too soon since last connection, delaying reconnect');
            _scheduleReconnect(); // Schedule another attempt
            return;
          }
        }

        print('WebSocketRepository: Attempting reconnection...');
        connect();
      } catch (e) {
        print('WebSocketRepository: Reconnection attempt failed: $e');
        // If reconnection fails, schedule another attempt
        _scheduleReconnect();
      }
    });
  }

  /// Send a message over the WebSocket, with error handling
  void _sendMessageSafely(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      // Not connected, can't send
      return;
    }

    try {
      // Encode and send the message
      final jsonMessage = jsonEncode(message);
      _channel!.sink.add(jsonMessage);
    } catch (e) {
      // Log error but don't crash
    }
  }



  /// Subscribe to a specific topic
  Future<void> subscribeToTopic(String topic) async {
    await runWithSocketExceptionGuard(() async {
      try {
        // Check if WebSocket URL is available before attempting to connect
        final wsUrl = GetIt.I<ServerEnvironmentManager>().state.profile?.wsUrl;
        if (wsUrl == null) {
          // Silently skip subscription when no server is configured
          return;
        }

        if (!_isConnected) {
          await connect();
        }

        if (_isConnected && !_activeSubscriptions.contains(topic)) {
          _activeSubscriptions.add(topic);

          // Create a controller for this topic if it doesn't exist
          _topicControllers[topic] ??= StreamController.broadcast();

          // No special handling needed for open_matches topic

          // Send subscribe message to the server
          _sendMessageSafely({
            'type': 'subscribe',
            'topic': topic,
          });
          print('Subscribed to topic: $topic');
        }
      } catch (e) {
        print('WebSocketRepository: Error subscribing to topic $topic: $e');
        // Don't rethrow, just log
      }
    });
  }
  
  /// Unsubscribe from a specific topic
  void unsubscribeFromTopic(String topic) {
    try {
      if (_isConnected && _activeSubscriptions.contains(topic)) {
        _activeSubscriptions.remove(topic);
        
        // Send unsubscribe message to the server
        _sendMessageSafely({
          'type': 'unsubscribe',
          'topic': topic,
        });
        print('Unsubscribed from topic: $topic');
      }
    } catch (e) {
      print('WebSocketRepository: Error unsubscribing from topic $topic: $e');
      // Don't rethrow, just log
    }
  }
  
  /// Send a custom message to the WebSocket server
  void sendMessage(Map<String, dynamic> message) {
    try {
      if (_isConnected) {
        _sendMessageSafely(message);
      } else {
        print('WebSocketRepository: Not connected, cannot send message');
      }
    } catch (e) {
      print('WebSocketRepository: Error sending message: $e');
      // Don't rethrow, just log
    }
  }

  /// Dispose of resources
  // Bridge code removed

  @disposeMethod
  void dispose() {
    disconnect();
    _reconnectTimer?.cancel();
    _matchUpdateController.close();
    _connectionStatusController.close();
    
    // Close all topic controllers
    for (final controller in _topicControllers.values) {
      controller.close();
    }
  }

  /// Safely close the WebSocket channel when app is closing
  Future<void> shutdown() async {
    await runWithSocketExceptionGuard(() async {
      print('WebSocketRepository: Shutting down WebSocket...');
      // Cancel any reconnection timer
      _reconnectTimer?.cancel();
      _reconnectTimer = null;
      
      // Close the channel
      try {
        disconnect();
      } catch (e) {
        print('WebSocketRepository: Error during shutdown: $e');
      }
      
      // Clear all controllers to prevent memory leaks
      try {
        for (final controller in _topicControllers.values) {
          await controller.close();
        }
        _topicControllers.clear();
        
        await _matchUpdateController.close();
        await _connectionStatusController.close();
      } catch (e) {
        print('WebSocketRepository: Error closing controllers during shutdown: $e');
      }
      
      print('WebSocketRepository: WebSocket shutdown complete');
    });
  }
}
